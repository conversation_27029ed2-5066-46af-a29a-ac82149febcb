import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useReconciliationStore from '+store/reconciliationStore';

import ReconcileReport from '../index';

const MockedReconcileReport = () => {
  return (
    <MockIndex>
      <ReconcileReport />
    </MockIndex>
  );
};

// Mock data for store
const mockStartReconciliationData = {
  processor: 'korapay',
  payment_type: 'payin' as const,
  report_start_date: '2024-01-01',
  report_end_date: '2024-01-31',
  processor_file_id: '123',
  processor_file_details: {
    key: 'test-file-key'
  },
  field_mapping: {
    processor: 'korapay',
    payment_type: 'payin' as const,
    primary_key_mappings: [],
    comparison_key_mappings: []
  }
};

const mockPrimaryKeyMappings = [
  {
    internal_report: 'transaction_id',
    processor_report: 'txn_id',
    color: '#FF5733'
  },
  {
    internal_report: 'amount',
    processor_report: 'amt',
    color: '#33FF57'
  }
];

const mockProcessorConfigResponse = {
  status: true,
  data: [
    {
      id: 1,
      kora_id: 1,
      processor: 'korapay',
      payment_type: 'payin',
      primary_key_mappings: [
        { internal_report: 'transaction_id', processor_report: 'txn_id' },
        { internal_report: 'amount', processor_report: 'amt' }
      ],
      comparison_key_mappings: [{ internal_report: 'status', processor_report: 'txn_status' }],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ]
};

const mockCreateReconciliationResponse = {
  status: true,
  message: 'Reconciliation created successfully',
  data: {
    id: 456,
    kora_id: 'KPY-REC-test123',
    title: 'Test Reconciliation',
    processor: 'korapay',
    payment_type: 'payin',
    status: 'processing',
    createdAt: '2024-01-01T00:00:00.000Z'
  }
};

describe('ReconcileReport', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');

        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(mockProcessorConfigResponse, { status: 200 });
        }
        return HttpResponse.json({ status: false, data: [] }, { status: 404 });
      }),

      http.post('/admin/settlement-reconciliations', () => {
        return HttpResponse.json(mockCreateReconciliationResponse, { status: 200 });
      })
    );
  });

  it('should be accessible', async () => {
    const { container } = render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const results = await axe(container, {
      rules: {
        label: { enabled: false },
        'color-contrast': { enabled: false }
      }
    });
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct heading and description', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getByText(/To start the reconciliation process, map each column/)).toBeInTheDocument();
  });

  it('should render report profile cards', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getAllByText('Uploaded Report')).toHaveLength(2);

    expect(screen.getAllByText('0 columns detected')).toHaveLength(2);
  });

  it('should display loading state while fetching processor config', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });
  });

  it('should render comparison key mappings when available', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
  });

  it('should render empty state when no comparison key mappings exist', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('No reconciliation columns')).toBeInTheDocument();
    });

    expect(screen.getByText('Add a column to start reconciliation')).toBeInTheDocument();
  });

  it('should handle adding new column', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Add new column')).toBeInTheDocument();
    });

    const addButton = screen.getByRole('button', { name: 'Add new column' });
    await user.click(addButton);

    const state = useReconciliationStore.getState();
    expect(state.comparisonKeyMappings).toHaveLength(1);
    expect(state.comparisonKeyMappings[0]).toEqual(
      expect.objectContaining({
        processor_report: '',
        internal_report: '',
        id: expect.any(String)
      })
    );
  });

  it('should handle deleting comparison key mapping', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTestId('trashIcon');
    await user.click(deleteButton);

    await waitFor(
      () => {
        const state = useReconciliationStore.getState();
        expect(state.comparisonKeyMappings).toHaveLength(0);
      },
      { timeout: 500 }
    );
  });

  it('should handle preview display toggle', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });
  });

  it('should handle modal close', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });

    const closeButton = screen.getByTestId('close-button');
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText('Processor Report Columns')).not.toBeInTheDocument();
    });
  });

  it('should disable start reconciliation button when conditions are not met', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Add new column')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).toBeDisabled();
  });

  it('should enable start reconciliation button when all conditions are met', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).not.toBeDisabled();
  });

  it('should handle start reconciliation submission', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    await user.click(startButton);

    await waitFor(() => {
      expect(startButton).toBeInTheDocument();
    });
  });

  it('should handle cancel button click', async () => {
    const user = userEvent.setup();

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    await user.click(cancelButton);

    const state = useReconciliationStore.getState();
    expect(state.startReconciliationData).toEqual({});
  });

  it('should handle empty reconciliation data', async () => {
    useReconciliationStore.setState({
      startReconciliationData: {} as any,
      primaryKeyMappings: [],
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });
  });

  it('should handle incomplete comparison key mappings', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [{ id: 'incomplete-mapping-1', internal_report: '', processor_report: '' }]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).toBeDisabled();
  });

  it('should display processor report columns in modal', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });

    expect(screen.getByText('txn_id')).toBeInTheDocument();
    expect(screen.getByText('amt')).toBeInTheDocument();
  });

  it('should handle API error during reconciliation creation', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'api-error-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    server.use(
      http.post('/admin/settlement-reconciliations', () => {
        return HttpResponse.json({ status: false, message: 'Failed to create reconciliation' }, { status: 400 });
      })
    );

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    await user.click(startButton);

    await waitFor(() => {
      expect(startButton).toBeInTheDocument();
    });
  });

  it('should set internal and processor report options from primary key mappings', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getAllByText('2 columns detected')).toHaveLength(2);
  });

  it('should handle removing item animation', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const deleteButton = screen.getByTestId('trashIcon');
    await user.click(deleteButton);

    await waitFor(
      () => {
        const state = useReconciliationStore.getState();
        expect(state.comparisonKeyMappings).toHaveLength(0);
      },
      { timeout: 500 }
    );
  });
});
